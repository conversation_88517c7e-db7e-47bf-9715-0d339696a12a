{"title": {"elements": [{"type": "textRun", "textRun": {"text": "一日一技：飞书文档转换为 Markdown", "style": {}, "location": {"zoneId": "0", "endIndex": 21}}}], "location": {"zoneId": "0", "endIndex": 21}, "lineId": "gkgML5"}, "body": {"blocks": [{"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "随着少数派逐渐 All in 飞书，我们少数派作者们也逐渐迁移到飞书文档进行写稿。飞书文档提供了 Web 平台的富文本编辑器，配合「少数派助手」这个服务，可以将稿件一键发布到少数派平台，着实是非常方便。", "style": {}, "location": {"zoneId": "0", "startIndex": 22, "endIndex": 123}}}], "location": {"zoneId": "0", "startIndex": 22, "endIndex": 123}, "lineId": "b8egkG"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "不少的少数派作者都有自己的博客平台，而大部分的博客平台都是使用 Markdown 作为输入从而生成 HTML 发布到网络中的。但是，飞书只支持 Markdown 语法的编辑，却不支持导出为 Markdown 文件下载，这打断了我们一直以来已经完善的发布博客流程。", "style": {}, "location": {"zoneId": "0", "startIndex": 125, "endIndex": 256}}}], "location": {"zoneId": "0", "startIndex": 124, "endIndex": 256}, "lineId": "oW69tp"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "本文就提供一种将飞书文档转换为 Markdown 文件的方法，来弥补这个 Gap。", "style": {}, "location": {"zoneId": "0", "startIndex": 258, "endIndex": 299}}}], "location": {"zoneId": "0", "startIndex": 257, "endIndex": 299}, "lineId": "ffLsEb"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "关联阅读：", "style": {}, "location": {"zoneId": "0", "startIndex": 301, "endIndex": 306}}}], "location": {"zoneId": "0", "startIndex": 300, "endIndex": 306}, "lineId": "cPxsOu"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "bullet", "indentLevel": 1}}, "elements": [{"type": "textRun", "textRun": {"text": "《内容团队协作的最佳形式：少数派编辑部如何用飞书》", "style": {"link": {"url": "https%3A%2F%2Fsspai.com%2Fpost%2F58509"}}, "location": {"zoneId": "0", "startIndex": 308, "endIndex": 333}}}], "location": {"zoneId": "0", "startIndex": 307, "endIndex": 333}, "lineId": "MIoLOy"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "bullet", "indentLevel": 1}}, "elements": [{"type": "textRun", "textRun": {"text": "《如何使用「少数派助手」从飞书文档发布文章》", "style": {"link": {"url": "https%3A%2F%2Fsspai.com%2Fpost%2F68135"}}, "location": {"zoneId": "0", "startIndex": 335, "endIndex": 357}}}], "location": {"zoneId": "0", "startIndex": 334, "endIndex": 357}, "lineId": "YQ75dX"}}, {"type": "paragraph", "paragraph": {"location": {"zoneId": "0", "startIndex": 358, "endIndex": 358}, "lineId": "NEKGOX"}}, {"type": "paragraph", "paragraph": {"style": {"headingLevel": 2}, "elements": [{"type": "textRun", "textRun": {"text": "现有的方法痛点", "style": {}, "location": {"zoneId": "0", "startIndex": 360, "endIndex": 367}}}], "location": {"zoneId": "0", "startIndex": 359, "endIndex": 367}, "lineId": "nezLRK"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "飞书支持的导出格式为 Word 和 PDF 两种格式。如需编辑，我们就只能选择 Word 格式，然后使用文档格式转换的瑞士军刀 pandoc 从 Word 文档转换为 Markdown 文件。参考命令：", "style": {}, "location": {"zoneId": "0", "startIndex": 369, "endIndex": 470}}}, {"type": "textRun", "textRun": {"text": "pandoc test.docx -o test.md", "style": {"codeInline": true}, "location": {"zoneId": "0", "startIndex": 470, "endIndex": 497}}}, {"type": "textRun", "textRun": {"text": " 。但是，如今这种方法已经不可靠了，如果尝试将本文转换，则会得到下图的格式。", "style": {}, "location": {"zoneId": "0", "startIndex": 497, "endIndex": 535}}}], "location": {"zoneId": "0", "startIndex": 368, "endIndex": 535}, "lineId": "mEDNfr"}}, {"type": "gallery", "gallery": {"galleryStyle": {}, "imageList": [{"fileToken": "boxcnA1QKPanfMhLxzF1eMhoArM", "width": 981, "height": 800}], "location": {"zoneId": "0", "startIndex": 536, "endIndex": 538}}}, {"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "从图中的效果可以看出，文档中多了很多冗余的换行，列表格式消失不见，图片丢失等问题。究其原因，是因为导出的 Word 文档没有使用 Word 内建的富文本样式，而全部使用的自定义样式。至于图片问题，转换后的 Markdown 文档中的图片格式是 ", "style": {}, "location": {"zoneId": "0", "startIndex": 539, "endIndex": 661}}}, {"type": "textRun", "textRun": {"text": "![Generated](media/image1.png){width=\"5.90625in\" height=\"2.8020833333333335in\"}", "style": {"codeInline": true}, "location": {"zoneId": "0", "startIndex": 661, "endIndex": 740}}}, {"type": "textRun", "textRun": {"text": " 。可以通过将 Word 文档的 docx 后缀改为 zip，然后从压缩包中整体提取 word/media 文件夹来修复图片的问题。但其它的格式问题，依然是一个头疼的问题。", "style": {}, "location": {"zoneId": "0", "startIndex": 740, "endIndex": 826}}}], "location": {"zoneId": "0", "startIndex": 539, "endIndex": 826}, "lineId": "UX3cCX"}}, {"type": "paragraph", "paragraph": {"style": {}, "location": {"zoneId": "0", "startIndex": 827, "endIndex": 828}, "lineId": "eiI1sv"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "另一方面，在没有 pandoc 转换工具的情况下，如果要获得 Markdown 文件，我理解的最便捷的方法如下：", "style": {}, "location": {"zoneId": "0", "startIndex": 830, "endIndex": 886}}}], "location": {"zoneId": "0", "startIndex": 829, "endIndex": 886}, "lineId": "0NdwmC"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "number", "indentLevel": 1, "number": 1}}, "elements": [{"type": "textRun", "textRun": {"text": "全文复制飞书文档的富文本内容", "style": {}, "location": {"zoneId": "0", "startIndex": 888, "endIndex": 902}}}], "location": {"zoneId": "0", "startIndex": 887, "endIndex": 902}, "lineId": "oMhk6a"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "number", "indentLevel": 1, "number": 2}}, "elements": [{"type": "textRun", "textRun": {"text": "全文粘贴到本地的 markdown 编辑器中", "style": {}, "location": {"zoneId": "0", "startIndex": 904, "endIndex": 926}}}], "location": {"zoneId": "0", "startIndex": 903, "endIndex": 926}, "lineId": "a0Q2RW"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "number", "indentLevel": 1, "number": 3}}, "elements": [{"type": "textRun", "textRun": {"text": "（可选）逐个下载文档中的图片并替换 markdown 文件中的图片", "style": {}, "location": {"zoneId": "0", "startIndex": 928, "endIndex": 961}}}], "location": {"zoneId": "0", "startIndex": 927, "endIndex": 961}, "lineId": "dbi13w"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "当完成第 2 步的时候，其实文档看起来已经完整了，但是仔细观察会发现文档中的图片是飞书的临时链接，且只有 24 小时的有效时间。因此，为了有效地保留图片，需要进行第 3 步手动下载图片替换。当一篇文档中的图片非常多的时候，手动下载替换是一个非常枯燥的事情。", "style": {}, "location": {"zoneId": "0", "startIndex": 963, "endIndex": 1091}}}], "location": {"zoneId": "0", "startIndex": 962, "endIndex": 1091}, "lineId": "M7YDBa"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "如果是使用图床的作者，可以在第 2 步的文档后直接使用图床上传工具（如：PicGo）进行图片上传快速替换，甚至 Typora 编辑器中就自带了这个功能。但是，由于图片链接是临时链接，没有文件后缀（.jpg/.png/.gif），当上传到图床后也丢失了这个信息，虽然不影响图床的回传，但是后面如果需要替换图床将会是一个灾难。", "style": {}, "location": {"zoneId": "0", "startIndex": 1093, "endIndex": 1254}}}], "location": {"zoneId": "0", "startIndex": 1092, "endIndex": 1254}, "lineId": "gn1qSA"}}, {"type": "paragraph", "paragraph": {"location": {"zoneId": "0", "startIndex": 1255, "endIndex": 1255}, "lineId": "o44iST"}}, {"type": "paragraph", "paragraph": {"style": {"headingLevel": 2}, "elements": [{"type": "textRun", "textRun": {"text": "使用 Feishu2Md 工具", "style": {}, "location": {"zoneId": "0", "startIndex": 1257, "endIndex": 1272}}}], "location": {"zoneId": "0", "startIndex": 1256, "endIndex": 1272}, "lineId": "U0UgLu"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "在进行了大量的搜索后，我其实也没有找到现有的转换工具能够转换飞书文档为 Markdown 文件下载的。但是，十分幸运，我碰巧找到了 ", "style": {}, "location": {"zoneId": "0", "startIndex": 1274, "endIndex": 1340}}}, {"type": "textRun", "textRun": {"text": "chyroc", "style": {"link": {"url": "https%3A%2F%2Fgithub.com%2Fchyroc"}}, "location": {"zoneId": "0", "startIndex": 1340, "endIndex": 1346}}}, {"type": "textRun", "textRun": {"text": " 使用飞书的 Open API 实现的飞书文档解析器 ", "style": {}, "location": {"zoneId": "0", "startIndex": 1346, "endIndex": 1373}}}, {"type": "textRun", "textRun": {"text": "lark_docs_md", "style": {"link": {"url": "https%3A%2F%2Fgithub.com%2Fchyroc%2Flark_docs_md"}}, "location": {"zoneId": "0", "startIndex": 1373, "endIndex": 1385}}}, {"type": "textRun", "textRun": {"text": " 。因此，我决定基于这个库开发一个下载工具，也就是小标题的 Feishu2Md 工具。", "style": {}, "location": {"zoneId": "0", "startIndex": 1385, "endIndex": 1428}}}], "location": {"zoneId": "0", "startIndex": 1273, "endIndex": 1428}, "lineId": "O7DfQk"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "Feishu2Md 已开源并发布在 Github中： https://github.com/Wsine/feishu2md", "style": {}, "location": {"zoneId": "0", "startIndex": 1430, "endIndex": 1491}}}], "location": {"zoneId": "0", "startIndex": 1429, "endIndex": 1491}, "lineId": "rimHf8"}}, {"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "下载 feishu2md ", "style": {"bold": true}, "location": {"zoneId": "0", "startIndex": 1492, "endIndex": 1505}}}, {"type": "textRun", "textRun": {"text": "-", "style": {}, "location": {"zoneId": "0", "startIndex": 1505, "endIndex": 1506}}}, {"type": "textRun", "textRun": {"text": " ", "style": {"bold": true}, "location": {"zoneId": "0", "startIndex": 1506, "endIndex": 1507}}}, {"type": "textRun", "textRun": {"text": "得益于 golang 本身的多平台编译特性，我已经为 Windows/Linux/Mac 都预编译了该工具的可执行文件，可以直接从 ", "style": {}, "location": {"zoneId": "0", "startIndex": 1507, "endIndex": 1573}}}, {"type": "textRun", "textRun": {"text": "Github Release", "style": {"link": {"url": "https%3A%2F%2Fgithub.com%2FWsine%2Ffeishu2md%2Freleases"}}, "location": {"zoneId": "0", "startIndex": 1573, "endIndex": 1587}}}, {"type": "textRun", "textRun": {"text": " 中下载，从压缩包中提取自己平台的 feishu2md 二进制可执行文件即可，建议放置在 PATH 路径中。", "style": {}, "location": {"zoneId": "0", "startIndex": 1587, "endIndex": 1641}}}], "location": {"zoneId": "0", "startIndex": 1492, "endIndex": 1641}, "lineId": "6sjfu7"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "生成配置文件", "style": {"bold": true}, "location": {"zoneId": "0", "startIndex": 1643, "endIndex": 1649}}}, {"type": "textRun", "textRun": {"text": " - feishu2md 需要使用飞书的 Open API 提取飞书文档，因此需要配置相应的 App ID 和 App Secret 进行 API 的调用。首先，进入飞书的 ", "style": {}, "location": {"zoneId": "0", "startIndex": 1649, "endIndex": 1736}}}, {"type": "textRun", "textRun": {"text": "开发者后台", "style": {"link": {"url": "https%3A%2F%2Fopen.feishu.cn%2Fapp"}}, "location": {"zoneId": "0", "startIndex": 1736, "endIndex": 1741}}}, {"type": "textRun", "textRun": {"text": " 然后创建一个企业自建应用，信息可以任意填，发布但不必等待审核通过。然后在创建的应用页面中，找到「凭证与基础信息」，即可找到 App ID 和 App Secret 信息。", "style": {}, "location": {"zoneId": "0", "startIndex": 1741, "endIndex": 1827}}}], "location": {"zoneId": "0", "startIndex": 1642, "endIndex": 1827}, "lineId": "3BG5s5"}}, {"type": "gallery", "gallery": {"galleryStyle": {}, "imageList": [{"fileToken": "boxcnf26PEqOA4MK6UySZtwtJqh", "width": 1168, "height": 555}], "location": {"zoneId": "0", "startIndex": 1828, "endIndex": 1830}}}, {"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "执行 ", "style": {}, "location": {"zoneId": "0", "startIndex": 1831, "endIndex": 1834}}}, {"type": "textRun", "textRun": {"text": "feishu2md --config", "style": {"codeInline": true}, "location": {"zoneId": "0", "startIndex": 1834, "endIndex": 1852}}}, {"type": "textRun", "textRun": {"text": " 命令会生成该工具的配置文件。生成的配置文件路径为：", "style": {}, "location": {"zoneId": "0", "startIndex": 1852, "endIndex": 1878}}}], "location": {"zoneId": "0", "startIndex": 1831, "endIndex": 1878}, "lineId": "4q3A4L"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "bullet", "indentLevel": 1, "number": 1}}, "elements": [{"type": "textRun", "textRun": {"text": "Windows: %AppData%/feishu2md/config.json", "style": {}, "location": {"zoneId": "0", "startIndex": 1880, "endIndex": 1920}}}], "location": {"zoneId": "0", "startIndex": 1879, "endIndex": 1920}, "lineId": "SReRuT"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "bullet", "indentLevel": 1, "number": 1}}, "elements": [{"type": "textRun", "textRun": {"text": "Linux: $XDG_CONFIG_HOME/feishu2md/config.json", "style": {}, "location": {"zoneId": "0", "startIndex": 1922, "endIndex": 1967}}}], "location": {"zoneId": "0", "startIndex": 1921, "endIndex": 1967}, "lineId": "x61d4P"}}, {"type": "paragraph", "paragraph": {"style": {"list": {"type": "bullet", "indentLevel": 1, "number": 1}}, "elements": [{"type": "textRun", "textRun": {"text": "Mac: $XDG_CONFIG_HOME/feishu2md/config.json", "style": {}, "location": {"zoneId": "0", "startIndex": 1969, "endIndex": 2012}}}], "location": {"zoneId": "0", "startIndex": 1968, "endIndex": 2012}, "lineId": "ohktdy"}}, {"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "如无配置 XDG_CONFIG_HOME 环境变量，则默认为 ~/.config 目录", "style": {}, "location": {"zoneId": "0", "startIndex": 2013, "endIndex": 2056}}}], "location": {"zoneId": "0", "startIndex": 2013, "endIndex": 2056}, "lineId": "DPN8dX"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "将 App ID 和 App Secret 填入配置文件 config.json 中的相应位置。另外，image_dir 配置项为存放文档中图片的文件夹名称。", "style": {}, "location": {"zoneId": "0", "startIndex": 2058, "endIndex": 2137}}}], "location": {"zoneId": "0", "startIndex": 2057, "endIndex": 2137}, "lineId": "SgYXks"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "下载飞书文档", "style": {"bold": true}, "location": {"zoneId": "0", "startIndex": 2139, "endIndex": 2145}}}, {"type": "textRun", "textRun": {"text": " - 通过 ", "style": {}, "location": {"zoneId": "0", "startIndex": 2145, "endIndex": 2151}}}, {"type": "textRun", "textRun": {"text": "feishu2md <你的飞书文档链接>", "style": {"codeInline": true}, "location": {"zoneId": "0", "startIndex": 2151, "endIndex": 2171}}}, {"type": "textRun", "textRun": {"text": " 直接下载，文档链接可以通过 分享 > 开启链接分享 > 复制链接 获得。", "style": {}, "location": {"zoneId": "0", "startIndex": 2171, "endIndex": 2208}}}], "location": {"zoneId": "0", "startIndex": 2138, "endIndex": 2208}, "lineId": "yE5MgH"}}, {"type": "gallery", "gallery": {"galleryStyle": {}, "imageList": [{"fileToken": "boxcn2z1WR9uDLU0WS5F24cpY1p", "width": 1538, "height": 488}], "location": {"zoneId": "0", "startIndex": 2209, "endIndex": 2211}}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "调用示例：", "style": {}, "location": {"zoneId": "0", "startIndex": 2213, "endIndex": 2218}}}], "location": {"zoneId": "0", "startIndex": 2212, "endIndex": 2218}, "lineId": "BEa8FY"}}, {"type": "code", "code": {"language": "<PERSON><PERSON>", "wrapContent": true, "body": {"blocks": [{"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "feishu2md ", "style": {}, "location": {"zoneId": "3umidqlevu", "endIndex": 10}}}, {"type": "docsLink", "docsLink": {"url": "https://oaztcemx3k.feishu.cn/docs/doccnrOvzeQ8BSnfsXj8jwJHC3c#", "location": {"zoneId": "3umidqlevu", "startIndex": 10, "endIndex": 31}}}], "location": {"zoneId": "3umidqlevu", "endIndex": 31}, "lineId": "fFhtqy"}}]}, "zoneId": "3umidqlevu", "location": {"zoneId": "0", "startIndex": 2219, "endIndex": 2220}}}, {"type": "gallery", "gallery": {"imageList": [{"fileToken": "boxcnKbWMvZeFbEJV4ZDcepCIDf", "width": 956, "height": 526}], "location": {"zoneId": "0", "startIndex": 2221, "endIndex": 2222}}}, {"type": "paragraph", "paragraph": {"elements": [{"type": "textRun", "textRun": {"text": "格式转换可能会有一些细微的渲染差异，毕竟 markdown 本身的标准也有很多套，建议手动检查一下。而最头疼的图片问题，该工具也已经帮忙整体处理好了。然后就可以愉快地用以前的工作流发布博客了。", "style": {}, "location": {"zoneId": "0", "startIndex": 2223, "endIndex": 2319}}}], "location": {"zoneId": "0", "startIndex": 2223, "endIndex": 2319}, "lineId": "Udvgz1"}}, {"type": "paragraph", "paragraph": {"style": {}, "location": {"zoneId": "0", "startIndex": 2320, "endIndex": 2321}, "lineId": "xx9HYV"}}, {"type": "paragraph", "paragraph": {"style": {"headingLevel": 2}, "elements": [{"type": "textRun", "textRun": {"text": "开发感言", "style": {}, "location": {"zoneId": "0", "startIndex": 2323, "endIndex": 2327}}}], "location": {"zoneId": "0", "startIndex": 2322, "endIndex": 2327}, "lineId": "1J9wYj"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "由于 lark_docs_md 是使用 golang 实现的，因此这也是我首次使用 golang 进行开发。对于开发小工具，整体的开发体验非常良好，而且还能编译得到二进制以及享受多平台编译的好处。工具可能还有一些不是很完善的地方，如有问题可以提 issue，我有时间会进行修复的。", "style": {}, "location": {"zoneId": "0", "startIndex": 2329, "endIndex": 2469}}}], "location": {"zoneId": "0", "startIndex": 2328, "endIndex": 2469}, "lineId": "aIZQha"}}, {"type": "paragraph", "paragraph": {"style": {}, "elements": [{"type": "textRun", "textRun": {"text": "最后，欢迎试用，欢迎 PR ~", "style": {}, "location": {"zoneId": "0", "startIndex": 2471, "endIndex": 2486}}}], "location": {"zoneId": "0", "startIndex": 2470, "endIndex": 2486}, "lineId": "3SyNhN"}}, {"type": "paragraph", "paragraph": {"location": {"zoneId": "0", "startIndex": 2487, "endIndex": 2487}, "lineId": "UDPCMY"}}]}}