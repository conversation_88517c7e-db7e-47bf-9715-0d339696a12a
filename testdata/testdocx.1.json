{"document": {"document_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "revision_id": 5, "title": "一日一技：飞书文档转换为 Markdown"}, "blocks": [{"block_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "children": ["doxcnImiEiyW6IiUkuwcvw0Qh6e", "doxcn46Yw4oIeyie4EHjiqBM3Fh", "doxcnsQuuQI2WMeECaZnWOa1kMz", "doxcnie0k4uEKcWM8iS0QleabMh", "doxcn8mQMICs2uY8qInSitNI3Qc", "doxcnC2iW62oqguqwRErjwN44l6", "doxcn0uKEMIUaMYAiENIL9CpR0f", "doxcns8Sg4e80OoIQu1PvN5OTqb", "doxcnq2oQi4QaCwaEaW21mVjOTe", "doxcngQGw4YEUGCsSSwKXFhNWcc", "doxcnA0AIwYQgOIO6oBS1uRz2Wh", "doxcnKC68aky4WY2uULNsc2H3jh", "doxcnWGummcoEySOuelWoUhyqes", "doxcnMosacmKOyYMGugsqopla5g", "doxcnUYg2E4GCuqsauybNTKaAZe", "doxcnoEiwAa6KIwYe6YmZr21Aqh", "doxcn8imAW4u0I2kGINN0INfWEd", "doxcnUweusKSE2SsyUTIcoHzXlb", "doxcngQ4eouoycu2gi6Hn1NFuoh", "doxcnI6SKUyA8Sk2k8HTGhqCT1f", "doxcnUkiAEKWAqGWOqKQKCGaf3g", "doxcnoOGIkKA2CwAyCmTL4iIcJf", "doxcnwCQK88O8QieoQry6dXOyqc", "doxcnCGogaoKsgIeY6Cwa6Endsb", "doxcnW24E4eOmeQMmSAQRi6XH8c", "doxcn8k4gaU8OWEmOEfWExG0Z0b", "doxcnqmui0cYuiGkmw5ziUR94Te", "doxcniauKuMSUWASgsNRaD1WqGg", "doxcnkKEIuEcUmE66Gtz4wJmPfq", "doxcnQ6QYcoeKACwUg3AbyGHZRd", "doxcn0sscyW4OMGiiMbDXH1pwrh", "doxcnAsKesUKQ4UuuMz1WGYAJde", "doxcnguAGUooC40YQOSKm9b2Nmh", "doxcnGUU8EqQeMoQWsfkfRFky6b", "doxcngieKsYWkoukGsdVT1bx4tg", "doxcnoOIm2qGiISgY40uZBoPNSb", "doxcnKACSkWMwaqUqK6sIUipxEw", "doxcnaOaCK4qywkogQfAjo6HUvg", "doxcnKGgaKWGqkgWNh6b8NjADzH", "doxcniCWK40AcmWWY4yMzGdf1oe", "doxcniIIGIEgEmQISiWogiZMpPf", "doxcnWWWSiCwOKuWI817qtuvcdg"], "block_type": 1, "page": {"style": {}, "elements": [{"text_run": {"content": "一日一技：飞书文档转换为 Markdown", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnImiEiyW6IiUkuwcvw0Qh6e", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "随着少数派逐渐 All in 飞书，我们少数派作者们也逐渐迁移到飞书文档进行写稿。飞书文档提供了 Web 平台的富文本编辑器，配合「少数派助手」这个服务，可以将稿件一键发布到少数派平台，着实是非常方便。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcn46Yw4oIeyie4EHjiqBM3Fh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "不少的少数派作者都有自己的博客平台，而大部分的博客平台都是使用 Markdown 作为输入从而生成 HTML 发布到网络中的。但是，飞书只支持 Markdown 语法的编辑，却不支持导出为 Markdown 文件下载，这打断了我们一直以来已经完善的发布博客流程。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnsQuuQI2WMeECaZnWOa1kMz", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "本文就提供一种将飞书文档转换为 Markdown 文件的方法，来弥补这个 Gap。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnie0k4uEKcWM8iS0QleabMh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "关联阅读：", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcn8mQMICs2uY8qInSitNI3Qc", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 12, "bullet": {"style": {}, "elements": [{"text_run": {"content": "《内容团队协作的最佳形式：少数派编辑部如何用飞书》", "text_element_style": {"link": {"url": "https%3A%2F%2Fsspai.com%2Fpost%2F58509"}}}}]}, "divider": {}}, {"block_id": "doxcnC2iW62oqguqwRErjwN44l6", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 12, "bullet": {"style": {}, "elements": [{"text_run": {"content": "《如何使用「少数派助手」从飞书文档发布文章》", "text_element_style": {"link": {"url": "https%3A%2F%2Fsspai.com%2Fpost%2F68135"}}}}]}, "divider": {}}, {"block_id": "doxcn0uKEMIUaMYAiENIL9CpR0f", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcns8Sg4e80OoIQu1PvN5OTqb", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 4, "heading2": {"style": {}, "elements": [{"text_run": {"content": "现有的方法痛点", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnq2oQi4QaCwaEaW21mVjOTe", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "飞书支持的导出格式为 Word 和 PDF 两种格式。如需编辑，我们就只能选择 Word 格式，然后使用文档格式转换的瑞士军刀 pandoc 从 Word 文档转换为 Markdown 文件。参考命令：", "text_element_style": {}}}, {"text_run": {"content": "pandoc test.docx -o test.md", "text_element_style": {"inline_code": true}}}, {"text_run": {"content": " 。但是，如今这种方法已经不可靠了，如果尝试将本文转换，则会得到下图的格式。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcngQGw4YEUGCsSSwKXFhNWcc", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 27, "divider": {}, "image": {"width": 981, "height": 800, "token": "boxcnbK20aJ9pePyziodIvjXTce"}}, {"block_id": "doxcnA0AIwYQgOIO6oBS1uRz2Wh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "从图中的效果可以看出，文档中多了很多冗余的换行，列表格式消失不见，图片丢失等问题。究其原因，是因为导出的 Word 文档没有使用 Word 内建的富文本样式，而全部使用的自定义样式。至于图片问题，转换后的 Markdown 文档中的图片格式是 ", "text_element_style": {}}}, {"text_run": {"content": "![Generated](media/image1.png){width=\"5.90625in\" height=\"2.8020833333333335in\"}", "text_element_style": {"inline_code": true}}}, {"text_run": {"content": " 。可以通过将 Word 文档的 docx 后缀改为 zip，然后从压缩包中整体提取 word/media 文件夹来修复图片的问题。但其它的格式问题，依然是一个头疼的问题。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnKC68aky4WY2uULNsc2H3jh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnWGummcoEySOuelWoUhyqes", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "另一方面，在没有 pandoc 转换工具的情况下，如果要获得 Markdown 文件，我理解的最便捷的方法如下：", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnMosacmKOyYMGugsqopla5g", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 13, "ordered": {"style": {}, "elements": [{"text_run": {"content": "全文复制飞书文档的富文本内容", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnUYg2E4GCuqsauybNTKaAZe", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 13, "ordered": {"style": {}, "elements": [{"text_run": {"content": "全文粘贴到本地的 markdown 编辑器中", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnoEiwAa6KIwYe6YmZr21Aqh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 13, "ordered": {"style": {}, "elements": [{"text_run": {"content": "（可选）逐个下载文档中的图片并替换 markdown 文件中的图片", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcn8imAW4u0I2kGINN0INfWEd", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "当完成第 2 步的时候，其实文档看起来已经完整了，但是仔细观察会发现文档中的图片是飞书的临时链接，且只有 24 小时的有效时间。因此，为了有效地保留图片，需要进行第 3 步手动下载图片替换。当一篇文档中的图片非常多的时候，手动下载替换是一个非常枯燥的事情。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnUweusKSE2SsyUTIcoHzXlb", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "如果是使用图床的作者，可以在第 2 步的文档后直接使用图床上传工具（如：PicGo）进行图片上传快速替换，甚至 Typora 编辑器中就自带了这个功能。但是，由于图片链接是临时链接，没有文件后缀（.jpg/.png/.gif），当上传到图床后也丢失了这个信息，虽然不影响图床的回传，但是后面如果需要替换图床将会是一个灾难。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcngQ4eouoycu2gi6Hn1NFuoh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnI6SKUyA8Sk2k8HTGhqCT1f", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 4, "heading2": {"style": {}, "elements": [{"text_run": {"content": "使用 Feishu2Md 工具", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnUkiAEKWAqGWOqKQKCGaf3g", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "在进行了大量的搜索后，我其实也没有找到现有的转换工具能够转换飞书文档为 Markdown 文件下载的。但是，十分幸运，我碰巧找到了 ", "text_element_style": {}}}, {"text_run": {"content": "chyroc", "text_element_style": {"link": {"url": "https%3A%2F%2Fgithub.com%2Fchyroc"}}}}, {"text_run": {"content": " 使用飞书的 Open API 实现的飞书文档解析器 ", "text_element_style": {}}}, {"text_run": {"content": "lark_docs_md", "text_element_style": {"link": {"url": "https%3A%2F%2Fgithub.com%2Fchyroc%2Flark_docs_md"}}}}, {"text_run": {"content": " 。因此，我决定基于这个库开发一个下载工具，也就是小标题的 Feishu2Md 工具。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnoOGIkKA2CwAyCmTL4iIcJf", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "Feishu2Md 已开源并发布在 Github中： https://github.com/Wsine/feishu2md", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnwCQK88O8QieoQry6dXOyqc", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "下载 feishu2md ", "text_element_style": {"bold": true}}}, {"text_run": {"content": "-", "text_element_style": {}}}, {"text_run": {"content": " ", "text_element_style": {"bold": true}}}, {"text_run": {"content": "得益于 golang 本身的多平台编译特性，我已经为 Windows/Linux/Mac 都预编译了该工具的可执行文件，可以直接从 ", "text_element_style": {}}}, {"text_run": {"content": "Github Release", "text_element_style": {"link": {"url": "https%3A%2F%2Fgithub.com%2FWsine%2Ffeishu2md%2Freleases"}}}}, {"text_run": {"content": " 中下载，从压缩包中提取自己平台的 feishu2md 二进制可执行文件即可，建议放置在 PATH 路径中。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnCGogaoKsgIeY6Cwa6Endsb", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "生成配置文件", "text_element_style": {"bold": true}}}, {"text_run": {"content": " - feishu2md 需要使用飞书的 Open API 提取飞书文档，因此需要配置相应的 App ID 和 App Secret 进行 API 的调用。首先，进入飞书的 ", "text_element_style": {}}}, {"text_run": {"content": "开发者后台", "text_element_style": {"link": {"url": "https%3A%2F%2Fopen.feishu.cn%2Fapp"}}}}, {"text_run": {"content": " 然后创建一个企业自建应用，信息可以任意填，发布但不必等待审核通过。然后在创建的应用页面中，找到「凭证与基础信息」，即可找到 App ID 和 App Secret 信息。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnW24E4eOmeQMmSAQRi6XH8c", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 27, "divider": {}, "image": {"width": 1168, "height": 555, "token": "boxcnh7JKLbFaWhHKHveYzGMNZg"}}, {"block_id": "doxcn8k4gaU8OWEmOEfWExG0Z0b", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "执行 ", "text_element_style": {}}}, {"text_run": {"content": "feishu2md --config", "text_element_style": {"inline_code": true}}}, {"text_run": {"content": " 命令会生成该工具的配置文件。生成的配置文件路径为：", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnqmui0cYuiGkmw5ziUR94Te", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 12, "bullet": {"style": {}, "elements": [{"text_run": {"content": "Windows: %AppData%/feishu2md/config.json", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcniauKuMSUWASgsNRaD1WqGg", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 12, "bullet": {"style": {}, "elements": [{"text_run": {"content": "Linux: $XDG_CONFIG_HOME/feishu2md/config.json", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnkKEIuEcUmE66Gtz4wJmPfq", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 12, "bullet": {"style": {}, "elements": [{"text_run": {"content": "Mac: $XDG_CONFIG_HOME/feishu2md/config.json", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnQ6QYcoeKACwUg3AbyGHZRd", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "如无配置 XDG_CONFIG_HOME 环境变量，则默认为 ~/.config 目录", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcn0sscyW4OMGiiMbDXH1pwrh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "将 App ID 和 App Secret 填入配置文件 config.json 中的相应位置。另外，image_dir 配置项为存放文档中图片的文件夹名称。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnAsKesUKQ4UuuMz1WGYAJde", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "下载飞书文档", "text_element_style": {"bold": true}}}, {"text_run": {"content": " - 通过 ", "text_element_style": {}}}, {"text_run": {"content": "feishu2md <你的飞书文档链接>", "text_element_style": {"inline_code": true}}}, {"text_run": {"content": " 直接下载，文档链接可以通过 分享 > 开启链接分享 > 复制链接 获得。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnguAGUooC40YQOSKm9b2Nmh", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 27, "divider": {}, "image": {"width": 1538, "height": 488, "token": "boxcnqt9YDTirkKlTATlQI025Ig"}}, {"block_id": "doxcnGUU8EqQeMoQWsfkfRFky6b", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "调用示例：", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcngieKsYWkoukGsdVT1bx4tg", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 14, "code": {"style": {"language": 7, "wrap": true}, "elements": [{"text_run": {"content": "feishu2md ", "text_element_style": {}}}, {"mention_doc": {"token": "doccnrOvzeQ8BSnfsXj8jwJHC3c", "obj_type": 1, "url": "https://oaztcemx3k.feishu.cn/docs/doccnrOvzeQ8BSnfsXj8jwJHC3c#", "title": "一日一技：飞书文档转换为 Markdown"}}]}, "divider": {}}, {"block_id": "doxcnoOIm2qGiISgY40uZBoPNSb", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 27, "divider": {}, "image": {"width": 956, "height": 526, "token": "boxcnAb2MgMQoUMDLLf3ySogueh"}}, {"block_id": "doxcnKACSkWMwaqUqK6sIUipxEw", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "格式转换可能会有一些细微的渲染差异，毕竟 markdown 本身的标准也有很多套，建议手动检查一下。而最头疼的图片问题，该工具也已经帮忙整体处理好了。然后就可以愉快地用以前的工作流发布博客了。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnaOaCK4qywkogQfAjo6HUvg", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnKGgaKWGqkgWNh6b8NjADzH", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 4, "heading2": {"style": {}, "elements": [{"text_run": {"content": "开发感言", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcniCWK40AcmWWY4yMzGdf1oe", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "由于 lark_docs_md 是使用 golang 实现的，因此这也是我首次使用 golang 进行开发。对于开发小工具，整体的开发体验非常良好，而且还能编译得到二进制以及享受多平台编译的好处。工具可能还有一些不是很完善的地方，如有问题可以提 issue，我有时间会进行修复的。", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcniIIGIEgEmQISiWogiZMpPf", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"content": "最后，欢迎试用，欢迎 PR ~", "text_element_style": {}}}]}, "divider": {}}, {"block_id": "doxcnWWWSiCwOKuWI817qtuvcdg", "parent_id": "doxcnXhd93zqoLnmVPGIPTy7AFe", "block_type": 2, "text": {"style": {}, "elements": [{"text_run": {"text_element_style": {}}}]}, "divider": {}}]}