name: release

on:
  release:
    types: [created]

jobs:
  releases-matrix:
    name: Release feishu2md binaries
    runs-on: ubuntu-latest
    strategy:
      matrix:
        # goos: [linux, windows, darwin]
        goos: [linux, windows]  # exclude darwin, see #40
        goarch: [amd64, arm64]
        exclude:
          - goarch: arm64
            goos: windows
    steps:
      - name: Checkout the code
        uses: actions/checkout@v2
      - name: Parallel build
        uses: wangyoucao577/go-release-action@v1.30
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          goos: ${{ matrix.goos }}
          goarch: ${{ matrix.goarch }}
          goversion: 1.17
          pre_command: export CGO_ENABLED=0
          ldflags: "-s -w -X main.version=${{ github.event.release.tag_name }}"
          executable_compression: "upx -9"
          project_path: "."
          binary_name: "feishu2md"
          extra_files: README.md
  releases-darwin:
    name: Release feishu2md binaries
    runs-on: ubuntu-latest
    strategy:
      matrix:
        goos: [darwin]  # specialize for dar<PERSON>, see #40
        goarch: [amd64, arm64]
    steps:
      - name: Checkout the code
        uses: actions/checkout@v2
      - name: Parallel build
        uses: wangyoucao577/go-release-action@v1.30
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          goos: ${{ matrix.goos }}
          goarch: ${{ matrix.goarch }}
          goversion: 1.17
          pre_command: export CGO_ENABLED=0
          ldflags: "-s -w -X main.version=${{ github.event.release.tag_name }}"
          project_path: "."
          binary_name: "feishu2md"
          extra_files: README.md
